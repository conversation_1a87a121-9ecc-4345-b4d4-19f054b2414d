"""
Version simplifiée de l'application Hugging Face
Utilise uniquement les modules de base pour tester la connectivité
"""

import requests
import json
import os
from typing import Dict, List, Optional

def test_huggingface_connection() -> bool:
    """Teste la connexion à Hugging Face Hub"""
    try:
        response = requests.get("https://huggingface.co/api/models?limit=1", timeout=10)
        return response.status_code == 200
    except Exception as e:
        print(f"Erreur de connexion: {e}")
        return False

def search_models(query: str = "", limit: int = 5) -> List[Dict]:
    """Recherche des modèles via l'API REST de Hugging Face"""
    try:
        url = f"https://huggingface.co/api/models"
        params = {
            "limit": limit,
            "search": query
        }
        
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            models = response.json()
            return [
                {
                    "id": model.get("id", "Unknown"),
                    "downloads": model.get("downloads", 0),
                    "likes": model.get("likes", 0),
                    "task": model.get("pipeline_tag", "Unknown")
                }
                for model in models
            ]
        else:
            print(f"Erreur API: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"Erreur lors de la recherche: {e}")
        return []

def test_model_inference(model_id: str, text: str, token: Optional[str] = None) -> Dict:
    """Teste l'inférence d'un modèle via l'API Inference"""
    try:
        url = f"https://api-inference.huggingface.co/models/{model_id}"
        
        headers = {
            "Content-Type": "application/json"
        }
        
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        payload = {"inputs": text}
        
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        if response.status_code == 200:
            return {"success": True, "result": response.json()}
        else:
            return {"success": False, "error": f"Status: {response.status_code}"}
            
    except Exception as e:
        return {"success": False, "error": str(e)}

def main():
    """Interface en ligne de commande simple"""
    print("🤗 Application Hugging Face - Version Simple")
    print("=" * 50)

    # Charger le token depuis .env
    from dotenv import load_dotenv
    load_dotenv()
    hf_token = os.getenv("HUGGINGFACE_API_TOKEN")

    if hf_token:
        print(f"✅ Token HF chargé: {hf_token[:10]}...")
    else:
        print("⚠️ Aucun token HF trouvé")

    # Test de connexion
    print("\n1. Test de connexion à Hugging Face Hub...")
    if test_huggingface_connection():
        print("✅ Connexion réussie !")
    else:
        print("❌ Problème de connexion")
        return

    # Recherche de modèles
    print("\n2. Recherche de modèles populaires...")
    models = search_models("", limit=5)

    if models:
        print(f"✅ {len(models)} modèles trouvés:")
        for i, model in enumerate(models, 1):
            print(f"   {i}. {model['id']} ({model['task']}) - {model['downloads']:,} téléchargements")
    else:
        print("❌ Aucun modèle trouvé")
        return

    # Test d'inférence simple avec token
    print("\n3. Test d'inférence avec un modèle simple...")
    test_model = "distilbert-base-uncased-finetuned-sst-2-english"
    test_text = "I love this application!"

    print(f"   Modèle: {test_model}")
    print(f"   Texte: '{test_text}'")
    print(f"   Token: {'Oui' if hf_token else 'Non'}")

    result = test_model_inference(test_model, test_text, hf_token)

    if result["success"]:
        print("✅ Inférence réussie !")
        print(f"   Résultat: {result['result']}")
    else:
        print(f"❌ Erreur d'inférence: {result['error']}")

    # Instructions pour la suite
    print("\n" + "=" * 50)
    print("🎯 Prochaines étapes:")
    print("1. Installez les dépendances: py -m pip install streamlit transformers torch")
    print("2. Lancez l'application complète: py -m streamlit run app.py")
    print("3. Ouvrez votre navigateur sur: http://localhost:8501")

    # Optionnel: demander un token
    print("\n💡 Votre token est configuré !")
    print("✅ Vous devriez maintenant avoir accès à plus de modèles")
    print("✅ Les limites de taux sont plus élevées")

if __name__ == "__main__":
    main()
