"""
Script de test pour vérifier le fonctionnement de l'application Hugging Face
"""

import sys
import importlib.util

def test_imports():
    """Teste si tous les modules nécessaires peuvent être importés"""
    print("🔍 Test des imports...")
    
    required_modules = [
        'streamlit',
        'transformers', 
        'torch',
        'huggingface_hub',
        'requests',
        'PIL',
        'numpy',
        'pandas',
        'dotenv'
    ]
    
    failed_imports = []
    
    for module in required_modules:
        try:
            if module == 'PIL':
                import PIL
            elif module == 'dotenv':
                import dotenv
            else:
                __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n❌ Modules manquants: {', '.join(failed_imports)}")
        return False
    else:
        print("\n✅ Tous les modules sont disponibles !")
        return True

def test_huggingface_utils():
    """Teste le module huggingface_utils"""
    print("\n🔍 Test du module huggingface_utils...")
    
    try:
        from huggingface_utils import HuggingFaceManager, get_device_info, get_model_suggestions
        print("✅ Import du module réussi")
        
        # Test des fonctions
        device_info = get_device_info()
        print(f"✅ Device info: {device_info['device']}")
        
        suggestions = get_model_suggestions("Génération de texte")
        print(f"✅ Suggestions obtenues: {len(suggestions)} modèles")
        
        # Test du manager (sans token)
        manager = HuggingFaceManager()
        print("✅ HuggingFaceManager initialisé")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur dans huggingface_utils: {e}")
        return False

def test_streamlit_app():
    """Teste si l'application Streamlit peut être chargée"""
    print("\n🔍 Test de l'application Streamlit...")
    
    try:
        # Vérifier que le fichier app.py existe et peut être lu
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'streamlit' in content and 'HuggingFaceManager' in content:
            print("✅ Fichier app.py valide")
            return True
        else:
            print("❌ Contenu de app.py invalide")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test de app.py: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 Démarrage des tests de l'application Hugging Face\n")
    
    tests = [
        ("Imports des modules", test_imports),
        ("Module huggingface_utils", test_huggingface_utils), 
        ("Application Streamlit", test_streamlit_app)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Test: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur inattendue: {e}")
            results.append((test_name, False))
    
    # Résumé
    print(f"\n{'='*50}")
    print("📊 RÉSUMÉ DES TESTS")
    print('='*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Résultat: {passed}/{len(results)} tests réussis")
    
    if passed == len(results):
        print("\n🎉 Tous les tests sont passés ! L'application est prête.")
        print("\n📝 Pour lancer l'application:")
        print("   py -m streamlit run app.py")
    else:
        print("\n⚠️ Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")

if __name__ == "__main__":
    main()
