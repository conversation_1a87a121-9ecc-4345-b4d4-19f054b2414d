import streamlit as st
import os
from dotenv import load_dotenv
import requests
from transformers import pipeline
from huggingface_hub import HfApi
import torch
from PIL import Image
import io
from huggingface_utils import HuggingFaceManager, get_device_info, get_model_suggestions

# Charger les variables d'environnement
load_dotenv()

# Configuration de la page
st.set_page_config(
    page_title="🤗 Hugging Face Explorer",
    page_icon="🤗",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Titre principal
st.title("🤗 Hugging Face Model Explorer")
st.markdown("Explorez et testez les modèles Hugging Face facilement !")

# Sidebar pour la configuration
st.sidebar.header("⚙️ Configuration")

# Token Hugging Face
hf_token = st.sidebar.text_input(
    "Token Hugging Face (optionnel)",
    type="password",
    value=os.getenv("HUGGINGFACE_API_TOKEN", ""),
    help="Nécessaire pour les modèles privés ou des limites plus élevées"
)

# Sélection du type de tâche
task_type = st.sidebar.selectbox(
    "Type de tâche",
    [
        "Génération de texte",
        "Classification de texte",
        "Résumé de texte",
        "Traduction",
        "Question-Réponse",
        "Classification d'images",
        "Génération d'images"
    ]
)

# Configuration des modèles par défaut
DEFAULT_MODELS = {
    "Génération de texte": "gpt2",
    "Classification de texte": "cardiffnlp/twitter-roberta-base-sentiment-latest",
    "Résumé de texte": "facebook/bart-large-cnn",
    "Traduction": "Helsinki-NLP/opus-mt-en-fr",
    "Question-Réponse": "distilbert-base-cased-distilled-squad",
    "Classification d'images": "google/vit-base-patch16-224",
    "Génération d'images": "stabilityai/stable-diffusion-2-1"
}

# Modèles compatibles avec l'API d'inférence pour la génération d'images
IMAGE_GENERATION_MODELS = [
    "stabilityai/stable-diffusion-2-1",
    "CompVis/stable-diffusion-v1-4",
    "runwayml/stable-diffusion-v1-5",
    "stabilityai/stable-diffusion-xl-base-1.0"
]

# Initialiser le gestionnaire Hugging Face
if 'hf_manager' not in st.session_state:
    st.session_state.hf_manager = HuggingFaceManager(hf_token if hf_token else None)

# Suggestions de modèles
suggestions = get_model_suggestions(task_type)
st.sidebar.subheader("💡 Modèles suggérés")
for suggestion in suggestions[:3]:
    if st.sidebar.button(f"📋 {suggestion}", key=f"suggest_{suggestion}"):
        st.session_state.selected_model = suggestion

# Sélection du modèle
model_name = st.sidebar.text_input(
    "Nom du modèle",
    value=getattr(st.session_state, 'selected_model', DEFAULT_MODELS.get(task_type, "")),
    help="Nom du modèle sur Hugging Face Hub"
)

# Validation du modèle
if model_name and st.sidebar.button("🔍 Valider le modèle"):
    with st.spinner("Validation du modèle..."):
        is_valid = st.session_state.hf_manager.validate_model(model_name)
        if is_valid:
            st.sidebar.success("✅ Modèle valide !")
            model_info = st.session_state.hf_manager.get_model_info(model_name)
            if model_info:
                st.sidebar.write(f"📊 **Téléchargements**: {model_info.get('downloads', 'N/A')}")
                st.sidebar.write(f"❤️ **Likes**: {model_info.get('likes', 'N/A')}")
        else:
            st.sidebar.error("❌ Modèle non trouvé ou inaccessible")

# Fonction pour tester la disponibilité d'un modèle de génération d'images
def test_image_model_availability(model_name, token=None):
    """Teste si un modèle de génération d'images est disponible"""
    try:
        url = f"https://api-inference.huggingface.co/models/{model_name}"
        headers = {"Content-Type": "application/json"}

        if token:
            headers["Authorization"] = f"Bearer {token}"

        # Test avec un prompt simple
        payload = {"inputs": "test"}
        response = requests.post(url, headers=headers, json=payload, timeout=10)

        if response.status_code == 200:
            return {"available": True, "status": "ready"}
        elif response.status_code == 503:
            return {"available": True, "status": "loading"}
        elif response.status_code == 404:
            return {"available": False, "status": "not_found"}
        else:
            return {"available": False, "status": f"error_{response.status_code}"}

    except Exception:
        return {"available": False, "status": "connection_error"}

# Fonction pour générer des images via l'API d'inférence
def generate_image_via_api(prompt, model_name, token=None):
    """Génère une image via l'API d'inférence Hugging Face"""
    try:
        url = f"https://api-inference.huggingface.co/models/{model_name}"
        headers = {"Content-Type": "application/json"}

        if token:
            headers["Authorization"] = f"Bearer {token}"

        payload = {"inputs": prompt}

        response = requests.post(url, headers=headers, json=payload, timeout=90)

        if response.status_code == 200:
            # Vérifier que la réponse contient bien une image
            if len(response.content) > 1000:  # Une image fait au moins quelques KB
                return {"success": True, "image_bytes": response.content}
            else:
                return {"success": False, "error": "Réponse invalide du modèle"}
        elif response.status_code == 503:
            return {"success": False, "error": "Modèle en cours de chargement. Veuillez réessayer dans quelques secondes."}
        elif response.status_code == 401:
            return {"success": False, "error": "Token invalide ou manquant"}
        elif response.status_code == 404:
            return {"success": False, "error": "Modèle non trouvé ou non disponible via l'API"}
        else:
            return {"success": False, "error": f"Erreur API {response.status_code}: {response.text[:200]}"}

    except requests.exceptions.Timeout:
        return {"success": False, "error": "Timeout - La génération a pris trop de temps"}
    except Exception as e:
        return {"success": False, "error": f"Erreur de connexion: {str(e)}"}

# Fonction pour charger un modèle
@st.cache_resource
def load_model(model_name, task_type, token=None):
    try:
        task_mapping = {
            "Génération de texte": "text-generation",
            "Classification de texte": "text-classification",
            "Résumé de texte": "summarization",
            "Traduction": "translation",
            "Question-Réponse": "question-answering",
            "Classification d'images": "image-classification"
        }

        task = task_mapping.get(task_type)
        if task:
            return pipeline(task, model=model_name, token=token)
        return None
    except Exception as e:
        st.error(f"Erreur lors du chargement du modèle : {str(e)}")
        return None

# Interface principale
col1, col2 = st.columns([2, 1])

with col1:
    st.header(f"🎯 {task_type}")
    
    if task_type == "Génération de texte":
        prompt = st.text_area("Entrez votre prompt :", height=100)
        max_length = st.slider("Longueur maximale", 10, 500, 100)
        
        if st.button("Générer") and prompt:
            with st.spinner("Génération en cours..."):
                model = load_model(model_name, task_type, hf_token)
                if model:
                    result = model(prompt, max_length=max_length, do_sample=True)
                    st.success("Texte généré :")
                    st.write(result[0]['generated_text'])
    
    elif task_type == "Classification de texte":
        text = st.text_area("Texte à classifier :", height=100)
        
        if st.button("Classifier") and text:
            with st.spinner("Classification en cours..."):
                model = load_model(model_name, task_type, hf_token)
                if model:
                    result = model(text)
                    st.success("Résultat de la classification :")
                    for item in result:
                        st.write(f"**{item['label']}**: {item['score']:.4f}")
    
    elif task_type == "Résumé de texte":
        text = st.text_area("Texte à résumer :", height=150)
        max_length = st.slider("Longueur max du résumé", 10, 200, 50)
        
        if st.button("Résumer") and text:
            with st.spinner("Résumé en cours..."):
                model = load_model(model_name, task_type, hf_token)
                if model:
                    result = model(text, max_length=max_length, do_sample=False)
                    st.success("Résumé :")
                    st.write(result[0]['summary_text'])
    
    elif task_type == "Traduction":
        text = st.text_area("Texte à traduire :", height=100)
        
        if st.button("Traduire") and text:
            with st.spinner("Traduction en cours..."):
                model = load_model(model_name, task_type, hf_token)
                if model:
                    result = model(text)
                    st.success("Traduction :")
                    st.write(result[0]['translation_text'])
    
    elif task_type == "Question-Réponse":
        context = st.text_area("Contexte :", height=100)
        question = st.text_input("Question :")
        
        if st.button("Répondre") and context and question:
            with st.spinner("Recherche de la réponse..."):
                model = load_model(model_name, task_type, hf_token)
                if model:
                    result = model(question=question, context=context)
                    st.success("Réponse :")
                    st.write(f"**Réponse**: {result['answer']}")
                    st.write(f"**Score**: {result['score']:.4f}")
    
    elif task_type == "Classification d'images":
        uploaded_file = st.file_uploader("Choisissez une image", type=['png', 'jpg', 'jpeg'])

        if uploaded_file and st.button("Classifier l'image"):
            with st.spinner("Classification en cours..."):
                image = Image.open(uploaded_file)
                st.image(image, caption="Image uploadée", use_column_width=True)

                model = load_model(model_name, task_type, hf_token)
                if model:
                    result = model(image)
                    st.success("Classification de l'image :")
                    for item in result:
                        st.write(f"**{item['label']}**: {item['score']:.4f}")

    elif task_type == "Génération d'images":
        st.info("🎨 Génération d'images via l'API d'inférence Hugging Face")

        # Test de disponibilité du modèle
        col1, col2 = st.columns([3, 1])
        with col1:
            if model_name not in IMAGE_GENERATION_MODELS:
                st.warning(f"⚠️ Le modèle '{model_name}' pourrait ne pas être optimisé pour la génération d'images.")

        with col2:
            if st.button("🔍 Tester le modèle"):
                with st.spinner("Test en cours..."):
                    availability = test_image_model_availability(model_name, hf_token)
                    if availability["available"]:
                        if availability["status"] == "ready":
                            st.success("✅ Modèle prêt")
                        elif availability["status"] == "loading":
                            st.warning("⏳ Modèle en chargement")
                    else:
                        st.error(f"❌ Modèle non disponible ({availability['status']})")

        # Modèles recommandés
        if model_name not in IMAGE_GENERATION_MODELS:
            with st.expander("💡 Modèles recommandés"):
                for recommended_model in IMAGE_GENERATION_MODELS:
                    if st.button(f"📋 {recommended_model}", key=f"rec_{recommended_model}"):
                        st.session_state.selected_model = recommended_model
                        st.rerun()

        # Interface de génération
        prompt = st.text_area(
            "Décrivez l'image que vous souhaitez générer :",
            height=100,
            placeholder="Ex: A beautiful sunset over mountains, digital art style, high quality, detailed"
        )

        # Options avancées
        with st.expander("⚙️ Options avancées et conseils"):
            st.write("**Conseils pour de meilleurs résultats :**")
            st.write("• Soyez précis dans votre description")
            st.write("• Mentionnez le style artistique souhaité")
            st.write("• Utilisez des mots-clés comme 'high quality', 'detailed', '4k'")
            st.write("• La génération peut prendre 30-90 secondes")
            st.write("• Certains modèles peuvent être temporairement indisponibles")

            st.write("**Exemples de prompts efficaces :**")
            example_prompts = [
                "A majestic dragon flying over a medieval castle, fantasy art, detailed",
                "Portrait of a wise old wizard, digital painting, high quality",
                "Futuristic city skyline at night, cyberpunk style, neon lights",
                "Beautiful landscape with mountains and lake, oil painting style"
            ]
            for example in example_prompts:
                if st.button(f"📝 {example}", key=f"example_{hash(example)}"):
                    st.session_state.example_prompt = example

        # Utiliser l'exemple sélectionné
        if hasattr(st.session_state, 'example_prompt'):
            prompt = st.session_state.example_prompt
            del st.session_state.example_prompt
            st.rerun()

        if st.button("🎨 Générer l'image") and prompt:
            with st.spinner("🎨 Génération en cours... Cela peut prendre jusqu'à 90 secondes"):
                result = generate_image_via_api(prompt, model_name, hf_token)

                if result["success"]:
                    # Afficher l'image générée
                    try:
                        image = Image.open(io.BytesIO(result["image_bytes"]))
                        st.success("✅ Image générée avec succès !")
                        st.image(image, caption=f"Prompt: {prompt[:50]}..." if len(prompt) > 50 else prompt, use_column_width=True)

                        # Informations sur l'image
                        st.write(f"**Modèle utilisé**: {model_name}")
                        st.write(f"**Taille de l'image**: {len(result['image_bytes'])} bytes")

                        # Option de téléchargement
                        st.download_button(
                            label="💾 Télécharger l'image",
                            data=result["image_bytes"],
                            file_name=f"generated_image_{hash(prompt) % 10000}.png",
                            mime="image/png"
                        )
                    except Exception as e:
                        st.error(f"❌ Erreur lors de l'affichage de l'image : {str(e)}")
                else:
                    st.error(f"❌ Erreur lors de la génération : {result['error']}")

                    # Messages d'aide spécifiques
                    if "503" in str(result['error']) or "chargement" in result['error']:
                        st.info("💡 Le modèle se charge. Réessayez dans quelques secondes.")
                        if st.button("🔄 Réessayer"):
                            st.rerun()
                    elif "401" in str(result['error']) or "Token" in str(result['error']):
                        st.warning("🔑 Problème d'authentification. Vérifiez votre token Hugging Face.")
                    elif "404" in str(result['error']) or "non trouvé" in result['error']:
                        st.warning("🔍 Ce modèle n'est pas disponible via l'API d'inférence.")
                        st.info("💡 Essayez un des modèles recommandés ci-dessus.")
                    elif "Timeout" in str(result['error']):
                        st.warning("⏱️ La génération a pris trop de temps. Essayez avec un prompt plus simple.")

with col2:
    st.header("📊 Informations")
    
    if model_name:
        st.write(f"**Modèle**: {model_name}")
        st.write(f"**Tâche**: {task_type}")
        
        # Lien vers le modèle
        st.markdown(f"[🔗 Voir sur Hugging Face](https://huggingface.co/{model_name})")
        
        # Informations système
        st.subheader("💻 Système")
        device_info = get_device_info()
        st.write(f"**PyTorch**: {torch.__version__}")
        st.write(f"**Device**: {device_info['device']}")
        st.write(f"**CUDA**: {device_info['cuda_available']}")
        if device_info['device'] == 'CUDA':
            st.write(f"**GPU**: {device_info.get('gpu_name', 'N/A')}")
            st.write(f"**Nombre de GPU**: {device_info['gpu_count']}")

        # Test de connexion
        st.subheader("🌐 Connexion")
        if st.button("🔄 Tester la connexion HF"):
            with st.spinner("Test en cours..."):
                is_connected = st.session_state.hf_manager.test_connection()
                if is_connected:
                    st.success("✅ Connexion réussie !")
                else:
                    st.error("❌ Problème de connexion")

# Footer
st.markdown("---")
st.markdown("Créé avec ❤️ et Streamlit | Powered by 🤗 Hugging Face")
