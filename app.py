import streamlit as st
import os
from dotenv import load_dotenv
import requests
from transformers import pipeline
from huggingface_hub import HfApi
import torch
from PIL import Image
import io
from huggingface_utils import HuggingFaceManager, get_device_info, get_model_suggestions

# Charger les variables d'environnement
load_dotenv()

# Configuration de la page
st.set_page_config(
    page_title="🤗 Hugging Face Explorer",
    page_icon="🤗",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Titre principal
st.title("🤗 Hugging Face Model Explorer")
st.markdown("Explorez et testez les modèles Hugging Face facilement !")

# Sidebar pour la configuration
st.sidebar.header("⚙️ Configuration")

# Token Hugging Face
hf_token = st.sidebar.text_input(
    "Token Hugging Face (optionnel)",
    type="password",
    value=os.getenv("HUGGINGFACE_API_TOKEN", ""),
    help="Nécessaire pour les modèles privés ou des limites plus élevées"
)

# Sélection du type de tâche
task_type = st.sidebar.selectbox(
    "Type de tâche",
    [
        "Génération de texte",
        "Classification de texte",
        "Résumé de texte",
        "Traduction",
        "Question-Réponse",
        "Classification d'images",
        "Génération d'images"
    ]
)

# Configuration des modèles par défaut
DEFAULT_MODELS = {
    "Génération de texte": "gpt2",
    "Classification de texte": "cardiffnlp/twitter-roberta-base-sentiment-latest",
    "Résumé de texte": "facebook/bart-large-cnn",
    "Traduction": "Helsinki-NLP/opus-mt-en-fr",
    "Question-Réponse": "distilbert-base-cased-distilled-squad",
    "Classification d'images": "google/vit-base-patch16-224",
    "Génération d'images": "runwayml/stable-diffusion-v1-5"
}

# Initialiser le gestionnaire Hugging Face
if 'hf_manager' not in st.session_state:
    st.session_state.hf_manager = HuggingFaceManager(hf_token if hf_token else None)

# Suggestions de modèles
suggestions = get_model_suggestions(task_type)
st.sidebar.subheader("💡 Modèles suggérés")
for suggestion in suggestions[:3]:
    if st.sidebar.button(f"📋 {suggestion}", key=f"suggest_{suggestion}"):
        st.session_state.selected_model = suggestion

# Sélection du modèle
model_name = st.sidebar.text_input(
    "Nom du modèle",
    value=getattr(st.session_state, 'selected_model', DEFAULT_MODELS.get(task_type, "")),
    help="Nom du modèle sur Hugging Face Hub"
)

# Validation du modèle
if model_name and st.sidebar.button("🔍 Valider le modèle"):
    with st.spinner("Validation du modèle..."):
        is_valid = st.session_state.hf_manager.validate_model(model_name)
        if is_valid:
            st.sidebar.success("✅ Modèle valide !")
            model_info = st.session_state.hf_manager.get_model_info(model_name)
            if model_info:
                st.sidebar.write(f"📊 **Téléchargements**: {model_info.get('downloads', 'N/A')}")
                st.sidebar.write(f"❤️ **Likes**: {model_info.get('likes', 'N/A')}")
        else:
            st.sidebar.error("❌ Modèle non trouvé ou inaccessible")

# Fonction pour charger un modèle
@st.cache_resource
def load_model(model_name, task_type, token=None):
    try:
        task_mapping = {
            "Génération de texte": "text-generation",
            "Classification de texte": "text-classification",
            "Résumé de texte": "summarization",
            "Traduction": "translation",
            "Question-Réponse": "question-answering",
            "Classification d'images": "image-classification"
        }
        
        task = task_mapping.get(task_type)
        if task:
            return pipeline(task, model=model_name, token=token)
        return None
    except Exception as e:
        st.error(f"Erreur lors du chargement du modèle : {str(e)}")
        return None

# Interface principale
col1, col2 = st.columns([2, 1])

with col1:
    st.header(f"🎯 {task_type}")
    
    if task_type == "Génération de texte":
        prompt = st.text_area("Entrez votre prompt :", height=100)
        max_length = st.slider("Longueur maximale", 10, 500, 100)
        
        if st.button("Générer") and prompt:
            with st.spinner("Génération en cours..."):
                model = load_model(model_name, task_type, hf_token)
                if model:
                    result = model(prompt, max_length=max_length, do_sample=True)
                    st.success("Texte généré :")
                    st.write(result[0]['generated_text'])
    
    elif task_type == "Classification de texte":
        text = st.text_area("Texte à classifier :", height=100)
        
        if st.button("Classifier") and text:
            with st.spinner("Classification en cours..."):
                model = load_model(model_name, task_type, hf_token)
                if model:
                    result = model(text)
                    st.success("Résultat de la classification :")
                    for item in result:
                        st.write(f"**{item['label']}**: {item['score']:.4f}")
    
    elif task_type == "Résumé de texte":
        text = st.text_area("Texte à résumer :", height=150)
        max_length = st.slider("Longueur max du résumé", 10, 200, 50)
        
        if st.button("Résumer") and text:
            with st.spinner("Résumé en cours..."):
                model = load_model(model_name, task_type, hf_token)
                if model:
                    result = model(text, max_length=max_length, do_sample=False)
                    st.success("Résumé :")
                    st.write(result[0]['summary_text'])
    
    elif task_type == "Traduction":
        text = st.text_area("Texte à traduire :", height=100)
        
        if st.button("Traduire") and text:
            with st.spinner("Traduction en cours..."):
                model = load_model(model_name, task_type, hf_token)
                if model:
                    result = model(text)
                    st.success("Traduction :")
                    st.write(result[0]['translation_text'])
    
    elif task_type == "Question-Réponse":
        context = st.text_area("Contexte :", height=100)
        question = st.text_input("Question :")
        
        if st.button("Répondre") and context and question:
            with st.spinner("Recherche de la réponse..."):
                model = load_model(model_name, task_type, hf_token)
                if model:
                    result = model(question=question, context=context)
                    st.success("Réponse :")
                    st.write(f"**Réponse**: {result['answer']}")
                    st.write(f"**Score**: {result['score']:.4f}")
    
    elif task_type == "Classification d'images":
        uploaded_file = st.file_uploader("Choisissez une image", type=['png', 'jpg', 'jpeg'])
        
        if uploaded_file and st.button("Classifier l'image"):
            with st.spinner("Classification en cours..."):
                image = Image.open(uploaded_file)
                st.image(image, caption="Image uploadée", use_column_width=True)
                
                model = load_model(model_name, task_type, hf_token)
                if model:
                    result = model(image)
                    st.success("Classification de l'image :")
                    for item in result:
                        st.write(f"**{item['label']}**: {item['score']:.4f}")

with col2:
    st.header("📊 Informations")
    
    if model_name:
        st.write(f"**Modèle**: {model_name}")
        st.write(f"**Tâche**: {task_type}")
        
        # Lien vers le modèle
        st.markdown(f"[🔗 Voir sur Hugging Face](https://huggingface.co/{model_name})")
        
        # Informations système
        st.subheader("💻 Système")
        device_info = get_device_info()
        st.write(f"**PyTorch**: {torch.__version__}")
        st.write(f"**Device**: {device_info['device']}")
        st.write(f"**CUDA**: {device_info['cuda_available']}")
        if device_info['device'] == 'CUDA':
            st.write(f"**GPU**: {device_info.get('gpu_name', 'N/A')}")
            st.write(f"**Nombre de GPU**: {device_info['gpu_count']}")

        # Test de connexion
        st.subheader("🌐 Connexion")
        if st.button("🔄 Tester la connexion HF"):
            with st.spinner("Test en cours..."):
                is_connected = st.session_state.hf_manager.test_connection()
                if is_connected:
                    st.success("✅ Connexion réussie !")
                else:
                    st.error("❌ Problème de connexion")

# Footer
st.markdown("---")
st.markdown("Créé avec ❤️ et Streamlit | Powered by 🤗 Hugging Face")
