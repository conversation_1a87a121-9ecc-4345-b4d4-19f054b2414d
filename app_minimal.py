"""
Application Streamlit minimale pour Hugging Face
Version ultra-simple pour tester rapidement
"""

import streamlit as st
import requests
import os
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

# Configuration de la page
st.set_page_config(
    page_title="🤗 HF Minimal",
    page_icon="🤗"
)

def test_hf_api(token=None):
    """Test simple de l'API Hugging Face"""
    try:
        headers = {}
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        response = requests.get(
            "https://api-inference.huggingface.co/models/distilbert-base-uncased-finetuned-sst-2-english",
            headers=headers,
            timeout=10
        )
        return response.status_code, response.text[:200]
    except Exception as e:
        return None, str(e)

# Interface principale
st.title("🤗 Hugging Face - Test Minimal")

# Token
token = st.text_input(
    "Token HF", 
    value=os.getenv("HUGGINGFACE_API_TOKEN", ""),
    type="password"
)

if token:
    st.success(f"✅ Token: {token[:10]}...")

# Test simple
if st.button("🧪 Tester l'API"):
    with st.spinner("Test en cours..."):
        status, response = test_hf_api(token)
        
        if status:
            st.write(f"**Status**: {status}")
            st.code(response)
        else:
            st.error(f"Erreur: {response}")

# Test d'inférence
st.header("Test d'inférence")
text = st.text_input("Texte à analyser", "I love this!")

if st.button("Analyser") and text:
    try:
        headers = {"Content-Type": "application/json"}
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        response = requests.post(
            "https://api-inference.huggingface.co/models/cardiffnlp/twitter-roberta-base-sentiment-latest",
            headers=headers,
            json={"inputs": text},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            st.success("✅ Résultat:")
            st.json(result)
        else:
            st.error(f"Erreur {response.status_code}: {response.text}")
            
    except Exception as e:
        st.error(f"Erreur: {str(e)}")

st.markdown("---")
st.write("🤗 Application minimale pour tester Hugging Face")
