"""
Guide et script pour installer la génération d'images en local
Alternative à l'API d'inférence Hugging Face
"""

import subprocess
import sys
import os

def install_requirements():
    """Installe les dépendances pour la génération d'images locale"""
    
    print("🎨 Installation de la génération d'images locale")
    print("=" * 50)
    
    # Packages requis
    packages = [
        "diffusers",
        "transformers", 
        "torch",
        "torchvision",
        "accelerate",
        "safetensors"
    ]
    
    print("📦 Installation des packages requis...")
    for package in packages:
        print(f"   Installing {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"   ✅ {package} installé")
        except subprocess.CalledProcessError:
            print(f"   ❌ Erreur lors de l'installation de {package}")
    
    print("\n✅ Installation terminée !")

def create_local_image_generator():
    """Crée un script de génération d'images local"""
    
    script_content = '''"""
Générateur d'images local avec Stable Diffusion
Utilise diffusers pour la génération locale
"""

import torch
from diffusers import StableDiffusionPipeline
from PIL import Image
import os

def generate_image_local(prompt, model_name="runwayml/stable-diffusion-v1-5", output_dir="generated_images"):
    """Génère une image localement avec Stable Diffusion"""
    
    print(f"🎨 Génération locale: {prompt}")
    print(f"📦 Modèle: {model_name}")
    
    try:
        # Créer le dossier de sortie
        os.makedirs(output_dir, exist_ok=True)
        
        # Charger le pipeline
        print("📥 Chargement du modèle...")
        pipe = StableDiffusionPipeline.from_pretrained(
            model_name,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
        )
        
        # Optimiser pour GPU si disponible
        if torch.cuda.is_available():
            pipe = pipe.to("cuda")
            print("🚀 Utilisation du GPU")
        else:
            print("💻 Utilisation du CPU (plus lent)")
        
        # Générer l'image
        print("🎨 Génération en cours...")
        image = pipe(prompt).images[0]
        
        # Sauvegarder
        filename = f"{output_dir}/generated_{hash(prompt) % 10000}.png"
        image.save(filename)
        
        print(f"✅ Image sauvegardée: {filename}")
        return filename
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def main():
    """Interface simple pour la génération locale"""
    
    print("🎨 Générateur d'Images Local")
    print("=" * 40)
    
    # Vérifier les dépendances
    try:
        import diffusers
        import torch
        print("✅ Dépendances installées")
    except ImportError as e:
        print(f"❌ Dépendance manquante: {e}")
        print("💡 Exécutez: pip install diffusers torch torchvision")
        return
    
    # Exemples de prompts
    example_prompts = [
        "A beautiful sunset over mountains, digital art",
        "Portrait of a wise wizard, fantasy art",
        "Futuristic city at night, cyberpunk style",
        "Cute cat in a garden, photorealistic"
    ]
    
    print("\\n💡 Exemples de prompts:")
    for i, prompt in enumerate(example_prompts, 1):
        print(f"   {i}. {prompt}")
    
    # Interface utilisateur
    while True:
        print("\\n" + "-" * 40)
        prompt = input("🎨 Entrez votre prompt (ou 'quit' pour quitter): ")
        
        if prompt.lower() in ['quit', 'exit', 'q']:
            break
        
        if prompt.strip():
            filename = generate_image_local(prompt)
            if filename:
                print(f"🖼️ Ouvrez le fichier: {filename}")
        else:
            print("⚠️ Veuillez entrer un prompt valide")

if __name__ == "__main__":
    main()
'''
    
    # Sauvegarder le script
    with open("local_image_generator.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print("📝 Script créé: local_image_generator.py")

def create_streamlit_local_app():
    """Crée une application Streamlit avec génération locale"""
    
    app_content = '''"""
Application Streamlit avec génération d'images locale
Utilise diffusers au lieu de l'API d'inférence
"""

import streamlit as st
import torch
from diffusers import StableDiffusionPipeline
from PIL import Image
import os
import io

st.set_page_config(
    page_title="🎨 Générateur Local",
    page_icon="🎨",
    layout="wide"
)

@st.cache_resource
def load_pipeline(model_name):
    """Charge le pipeline de génération (mis en cache)"""
    try:
        pipe = StableDiffusionPipeline.from_pretrained(
            model_name,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
        )
        
        if torch.cuda.is_available():
            pipe = pipe.to("cuda")
        
        return pipe
    except Exception as e:
        st.error(f"Erreur de chargement: {e}")
        return None

def main():
    st.title("🎨 Générateur d'Images Local")
    st.markdown("Génération d'images avec Stable Diffusion en local")
    
    # Vérification des dépendances
    try:
        import diffusers
        st.success("✅ Diffusers installé")
    except ImportError:
        st.error("❌ Diffusers non installé")
        st.code("pip install diffusers torch torchvision")
        return
    
    # Configuration
    model_name = st.selectbox(
        "Modèle Stable Diffusion",
        [
            "runwayml/stable-diffusion-v1-5",
            "CompVis/stable-diffusion-v1-4",
            "stabilityai/stable-diffusion-2-1"
        ]
    )
    
    # Interface de génération
    prompt = st.text_area(
        "Prompt pour la génération",
        "A beautiful sunset over mountains, digital art style",
        height=100
    )
    
    if st.button("🎨 Générer l'image"):
        if prompt:
            with st.spinner("Chargement du modèle et génération..."):
                pipe = load_pipeline(model_name)
                
                if pipe:
                    try:
                        image = pipe(prompt).images[0]
                        
                        st.success("✅ Image générée !")
                        st.image(image, caption=prompt, use_column_width=True)
                        
                        # Téléchargement
                        buf = io.BytesIO()
                        image.save(buf, format="PNG")
                        
                        st.download_button(
                            "💾 Télécharger",
                            buf.getvalue(),
                            f"generated_{hash(prompt) % 10000}.png",
                            "image/png"
                        )
                        
                    except Exception as e:
                        st.error(f"Erreur de génération: {e}")
        else:
            st.warning("Veuillez entrer un prompt")

if __name__ == "__main__":
    main()
'''
    
    with open("streamlit_local_generator.py", "w", encoding="utf-8") as f:
        f.write(app_content)
    
    print("📝 Application Streamlit créée: streamlit_local_generator.py")

def main():
    """Guide principal d'installation"""
    
    print("🎨 Guide d'Installation - Génération d'Images Locale")
    print("=" * 60)
    
    print("\n📋 ÉTAPES D'INSTALLATION:")
    print("1. Installer les dépendances Python")
    print("2. Créer les scripts de génération")
    print("3. Tester la génération locale")
    
    choice = input("\n❓ Voulez-vous installer automatiquement ? (y/n): ")
    
    if choice.lower() in ['y', 'yes', 'oui']:
        print("\n🚀 Installation automatique...")
        install_requirements()
        create_local_image_generator()
        create_streamlit_local_app()
        
        print("\n✅ INSTALLATION TERMINÉE !")
        print("\n🎯 PROCHAINES ÉTAPES:")
        print("1. Testez: python local_image_generator.py")
        print("2. Ou lancez: streamlit run streamlit_local_generator.py")
        print("3. La première génération sera plus lente (téléchargement du modèle)")
        
    else:
        print("\n📝 INSTALLATION MANUELLE:")
        print("1. pip install diffusers torch torchvision accelerate")
        print("2. Créez un script Python avec le code de génération")
        print("3. Utilisez StableDiffusionPipeline de diffusers")
        
    print("\n💡 AVANTAGES DE L'INSTALLATION LOCALE:")
    print("✅ Pas de limitations d'API")
    print("✅ Génération illimitée")
    print("✅ Contrôle total des paramètres")
    print("✅ Pas besoin de token")
    print("✅ Fonctionne hors ligne")

if __name__ == "__main__":
    main()
