"""
Test simple du token Hugging Face
"""

import requests
import os
from dotenv import load_dotenv
import json

# Charger les variables d'environnement
load_dotenv()

def test_token():
    token = os.getenv("HUGGINGFACE_API_TOKEN")
    
    print("🤗 Test du Token Hugging Face")
    print("=" * 40)
    
    if token:
        print(f"✅ Token trouvé: {token[:10]}...")
    else:
        print("❌ Aucun token trouvé dans .env")
        return
    
    # Test 1: API Hub
    print("\n1. Test API Hub...")
    try:
        response = requests.get(
            "https://huggingface.co/api/models?limit=3",
            headers={"Authorization": f"Bearer {token}"},
            timeout=10
        )
        if response.status_code == 200:
            models = response.json()
            print(f"✅ API Hub OK - {len(models)} modèles récupérés")
            for model in models[:2]:
                print(f"   - {model.get('id', 'Unknown')}")
        else:
            print(f"❌ API Hub erreur: {response.status_code}")
    except Exception as e:
        print(f"❌ API Hub erreur: {e}")
    
    # Test 2: API Inference
    print("\n2. Test API Inference...")
    try:
        response = requests.post(
            "https://api-inference.huggingface.co/models/cardiffnlp/twitter-roberta-base-sentiment-latest",
            headers={
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            },
            json={"inputs": "I love this test!"},
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API Inference OK")
            print(f"Résultat: {json.dumps(result, indent=2)}")
        elif response.status_code == 503:
            print("⚠️ Modèle en cours de chargement, réessayez dans quelques secondes")
        else:
            print(f"❌ API Inference erreur: {response.text}")
    
    except Exception as e:
        print(f"❌ API Inference erreur: {e}")
    
    # Test 3: Modèle simple
    print("\n3. Test modèle simple...")
    try:
        response = requests.post(
            "https://api-inference.huggingface.co/models/gpt2",
            headers={
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            },
            json={"inputs": "Hello, my name is"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ GPT-2 OK")
            if isinstance(result, list) and len(result) > 0:
                print(f"Génération: {result[0].get('generated_text', 'N/A')}")
        else:
            print(f"❌ GPT-2 erreur: {response.status_code}")
    
    except Exception as e:
        print(f"❌ GPT-2 erreur: {e}")
    
    print("\n" + "=" * 40)
    print("🎯 Résumé:")
    print("- Votre token est configuré et fonctionne")
    print("- Vous pouvez maintenant utiliser l'API Hugging Face")
    print("- Certains modèles peuvent prendre du temps à se charger")

if __name__ == "__main__":
    test_token()
