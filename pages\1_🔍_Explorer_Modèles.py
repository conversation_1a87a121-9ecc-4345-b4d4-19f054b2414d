import streamlit as st
import os
import sys
from pathlib import Path

# Ajouter le répertoire parent au path pour importer huggingface_utils
parent_dir = Path(__file__).parent.parent
sys.path.append(str(parent_dir))

from huggingface_utils import HuggingFaceManager
from dotenv import load_dotenv

load_dotenv()

st.set_page_config(
    page_title="🔍 Explorer les Modèles",
    page_icon="🔍",
    layout="wide"
)

st.title("🔍 Explorateur de Modèles Hugging Face")
st.markdown("Découvrez et recherchez parmi des milliers de modèles disponibles sur Hugging Face Hub")

# Configuration dans la sidebar
st.sidebar.header("⚙️ Configuration")
hf_token = st.sidebar.text_input(
    "Token Hugging Face (optionnel)",
    type="password",
    value=os.getenv("HUGGINGFACE_API_TOKEN", "")
)

# Initialiser le gestionnaire
if 'hf_manager' not in st.session_state:
    st.session_state.hf_manager = HuggingFaceManager(hf_token if hf_token else None)

# Onglets pour différentes vues
tab1, tab2, tab3 = st.tabs(["🔍 Recherche", "🏆 Populaires", "📊 Statistiques"])

with tab1:
    st.header("Recherche de Modèles")
    
    col1, col2 = st.columns([3, 1])
    
    with col1:
        search_query = st.text_input("🔍 Rechercher des modèles", placeholder="Ex: sentiment, translation, gpt...")
    
    with col2:
        search_limit = st.selectbox("Nombre de résultats", [5, 10, 20, 50], index=1)
    
    # Filtres
    st.subheader("🎯 Filtres")
    col1, col2 = st.columns(2)
    
    with col1:
        task_filter = st.selectbox(
            "Type de tâche",
            ["Tous", "text-generation", "text-classification", "summarization", 
             "translation", "question-answering", "image-classification", "text-to-image"],
            index=0
        )
    
    with col2:
        library_filter = st.selectbox(
            "Bibliothèque",
            ["Toutes", "transformers", "diffusers", "sentence-transformers", "timm"],
            index=0
        )
    
    if st.button("🔍 Rechercher") or search_query:
        if search_query:
            with st.spinner("Recherche en cours..."):
                task = None if task_filter == "Tous" else task_filter
                models = st.session_state.hf_manager.search_models(
                    search_query, 
                    task=task, 
                    limit=search_limit
                )
                
                if models:
                    st.success(f"✅ {len(models)} modèles trouvés")
                    
                    for model in models:
                        with st.expander(f"📦 {model['id']} - {model['task']}"):
                            col1, col2, col3 = st.columns(3)
                            
                            with col1:
                                st.metric("📥 Téléchargements", f"{model['downloads']:,}")
                            
                            with col2:
                                st.metric("❤️ Likes", model['likes'])
                            
                            with col3:
                                st.write(f"**Bibliothèque**: {model['library']}")
                            
                            # Boutons d'action
                            col1, col2 = st.columns(2)
                            with col1:
                                if st.button(f"📋 Copier le nom", key=f"copy_{model['id']}"):
                                    st.code(model['id'])
                            
                            with col2:
                                st.markdown(f"[🔗 Voir sur HF Hub](https://huggingface.co/{model['id']})")
                else:
                    st.warning("Aucun modèle trouvé pour cette recherche")

with tab2:
    st.header("🏆 Modèles Populaires par Catégorie")
    
    categories = [
        "text-generation",
        "text-classification", 
        "summarization",
        "translation",
        "question-answering",
        "image-classification"
    ]
    
    selected_category = st.selectbox("Choisir une catégorie", categories)
    
    if st.button("📊 Charger les modèles populaires"):
        with st.spinner("Chargement des modèles populaires..."):
            popular_models = st.session_state.hf_manager.search_models(
                "", 
                task=selected_category, 
                limit=10
            )
            
            if popular_models:
                st.subheader(f"Top 10 - {selected_category}")
                
                for i, model in enumerate(popular_models, 1):
                    with st.container():
                        col1, col2, col3, col4 = st.columns([1, 4, 2, 2])
                        
                        with col1:
                            st.write(f"**#{i}**")
                        
                        with col2:
                            st.write(f"**{model['id']}**")
                            st.caption(f"Bibliothèque: {model['library']}")
                        
                        with col3:
                            st.metric("📥", f"{model['downloads']:,}")
                        
                        with col4:
                            st.metric("❤️", model['likes'])
                        
                        st.markdown("---")

with tab3:
    st.header("📊 Statistiques et Informations")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("🎯 Tâches Supportées")
        tasks_info = {
            "Génération de texte": "Créer du texte à partir d'un prompt",
            "Classification de texte": "Classifier le sentiment, les émotions, etc.",
            "Résumé": "Résumer des textes longs",
            "Traduction": "Traduire entre différentes langues",
            "Question-Réponse": "Répondre à des questions sur un contexte",
            "Classification d'images": "Identifier le contenu d'images"
        }
        
        for task, description in tasks_info.items():
            st.write(f"**{task}**: {description}")
    
    with col2:
        st.subheader("💡 Conseils d'Utilisation")
        tips = [
            "🔑 Utilisez un token HF pour accéder aux modèles privés",
            "⚡ Les modèles plus petits sont plus rapides",
            "🎯 Choisissez le modèle selon votre tâche spécifique",
            "📊 Vérifiez les téléchargements pour la popularité",
            "🔍 Lisez la documentation du modèle sur HF Hub",
            "💾 Certains modèles nécessitent plus de mémoire"
        ]
        
        for tip in tips:
            st.write(tip)
    
    # Informations sur l'API
    st.subheader("🔧 Informations API")
    if st.button("🔄 Tester la connexion"):
        with st.spinner("Test de connexion..."):
            is_connected = st.session_state.hf_manager.test_connection()
            if is_connected:
                st.success("✅ Connexion à Hugging Face Hub réussie !")
            else:
                st.error("❌ Problème de connexion")
    
    st.info("""
    **Note**: Cette application utilise l'API publique de Hugging Face. 
    Pour des limites plus élevées et l'accès aux modèles privés, 
    configurez votre token d'authentification.
    """)

# Footer
st.markdown("---")
st.markdown("🤗 Explorez l'écosystème Hugging Face | Créé avec Streamlit")
