"""
Application Streamlit Hugging Face corrigée et optimisée
Version avec génération d'images fonctionnelle
"""

import streamlit as st
import requests
import os
from dotenv import load_dotenv
from PIL import Image
import io

# Charger les variables d'environnement
load_dotenv()

# Configuration de la page
st.set_page_config(
    page_title="🤗 Hugging Face Explorer - Corrigé",
    page_icon="🤗",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Modèles recommandés pour la génération d'images
# Note: La plupart des modèles Stable Diffusion ne sont pas disponibles via l'API gratuite
IMAGE_GENERATION_MODELS = [
    "dreamlike-art/dreamlike-diffusion-1.0",
    "prompthero/openjourney",
    "nitrosocke/Arcane-Diffusion",
    "stabilityai/stable-diffusion-2-1",
    "CompVis/stable-diffusion-v1-4",
    "runwayml/stable-diffusion-v1-5"
]

# Modèles alternatifs qui pourraient être disponibles
ALTERNATIVE_MODELS = [
    "flax-community/dalle-mini",
    "craiyon/dalle-mini",
    "dalle-mini/dalle-mini"
]

def test_image_model_availability(model_name, token=None):
    """Teste si un modèle de génération d'images est disponible"""
    try:
        url = f"https://api-inference.huggingface.co/models/{model_name}"
        headers = {"Content-Type": "application/json"}
        
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        payload = {"inputs": "test"}
        response = requests.post(url, headers=headers, json=payload, timeout=10)
        
        if response.status_code == 200:
            return {"available": True, "status": "ready"}
        elif response.status_code == 503:
            return {"available": True, "status": "loading"}
        elif response.status_code == 404:
            return {"available": False, "status": "not_found"}
        else:
            return {"available": False, "status": f"error_{response.status_code}"}
    
    except Exception:
        return {"available": False, "status": "connection_error"}

def generate_image_via_api(prompt, model_name, token=None):
    """Génère une image via l'API d'inférence Hugging Face"""
    try:
        url = f"https://api-inference.huggingface.co/models/{model_name}"
        headers = {"Content-Type": "application/json"}
        
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        payload = {"inputs": prompt}
        
        response = requests.post(url, headers=headers, json=payload, timeout=90)
        
        if response.status_code == 200:
            if len(response.content) > 1000:
                return {"success": True, "image_bytes": response.content}
            else:
                return {"success": False, "error": "Réponse invalide du modèle"}
        elif response.status_code == 503:
            return {"success": False, "error": "Modèle en cours de chargement"}
        elif response.status_code == 401:
            return {"success": False, "error": "Token invalide ou manquant"}
        elif response.status_code == 404:
            return {"success": False, "error": "Modèle non disponible via l'API"}
        else:
            return {"success": False, "error": f"Erreur API {response.status_code}"}
    
    except requests.exceptions.Timeout:
        return {"success": False, "error": "Timeout - Génération trop longue"}
    except Exception as e:
        return {"success": False, "error": f"Erreur: {str(e)}"}

def test_text_model_inference(model_id, text, token=None):
    """Teste l'inférence d'un modèle de texte"""
    try:
        url = f"https://api-inference.huggingface.co/models/{model_id}"
        headers = {"Content-Type": "application/json"}
        
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        payload = {"inputs": text}
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        if response.status_code == 200:
            return {"success": True, "result": response.json()}
        else:
            return {"success": False, "error": f"Status {response.status_code}"}
    
    except Exception as e:
        return {"success": False, "error": str(e)}

# Interface principale
st.title("🤗 Hugging Face Explorer - Version Corrigée")
st.markdown("Explorez et testez les modèles Hugging Face avec génération d'images fonctionnelle !")

# Sidebar
st.sidebar.header("⚙️ Configuration")

# Token
default_token = os.getenv("HUGGINGFACE_API_TOKEN", "")
hf_token = st.sidebar.text_input(
    "Token Hugging Face",
    type="password",
    value=default_token,
    help="Chargé automatiquement depuis .env"
)

if hf_token:
    st.sidebar.success(f"✅ Token configuré ({hf_token[:10]}...)")
else:
    st.sidebar.warning("⚠️ Aucun token configuré")

# Sélection du type de tâche
task_type = st.sidebar.selectbox(
    "Type de tâche",
    [
        "Génération d'images",
        "Analyse de sentiment",
        "Génération de texte",
        "Description d'images (Alternative)"
    ]
)

# Interface principale avec onglets
tab1, tab2, tab3 = st.tabs(["🎨 Génération", "🧪 Test", "📊 Informations"])

with tab1:
    if task_type == "Génération d'images":
        st.header("🎨 Génération d'Images")

        # Avertissement sur les limitations
        st.warning("⚠️ **Limitation importante**: La plupart des modèles de génération d'images (comme Stable Diffusion) ne sont pas disponibles via l'API d'inférence gratuite de Hugging Face.")

        with st.expander("💡 Alternatives recommandées"):
            st.write("**Pour la génération d'images, vous pouvez :**")
            st.write("1. 🔑 **Utiliser l'API payante** de Hugging Face Inference Endpoints")
            st.write("2. 💻 **Installer localement** avec `pip install diffusers`")
            st.write("3. 🌐 **Utiliser d'autres services** comme OpenAI DALL-E, Midjourney")
            st.write("4. 🧪 **Tester les modèles** ci-dessous (disponibilité limitée)")

            st.code("""
# Exemple d'installation locale
pip install diffusers torch torchvision

# Code Python pour génération locale
from diffusers import StableDiffusionPipeline
pipe = StableDiffusionPipeline.from_pretrained("runwayml/stable-diffusion-v1-5")
image = pipe("A beautiful sunset").images[0]
            """)

        # Sélection du modèle
        model_name = st.selectbox(
            "Modèle de génération d'images (disponibilité limitée)",
            IMAGE_GENERATION_MODELS + ALTERNATIVE_MODELS,
            help="Ces modèles peuvent ne pas être disponibles via l'API gratuite"
        )
        
        # Test de disponibilité
        col1, col2 = st.columns([3, 1])
        with col2:
            if st.button("🔍 Tester le modèle"):
                with st.spinner("Test..."):
                    availability = test_image_model_availability(model_name, hf_token)
                    if availability["available"]:
                        if availability["status"] == "ready":
                            st.success("✅ Prêt")
                        else:
                            st.warning("⏳ Chargement")
                    else:
                        st.error(f"❌ {availability['status']}")
        
        # Interface de génération
        prompt = st.text_area(
            "Décrivez l'image à générer :",
            height=100,
            placeholder="Ex: A beautiful sunset over mountains, digital art style, high quality"
        )
        
        # Exemples de prompts
        with st.expander("💡 Exemples de prompts"):
            examples = [
                "A majestic dragon flying over a castle, fantasy art",
                "Portrait of a wise wizard, digital painting",
                "Futuristic city at night, cyberpunk style",
                "Beautiful landscape with mountains, oil painting"
            ]
            for example in examples:
                if st.button(f"📝 {example}", key=f"ex_{hash(example)}"):
                    st.session_state.selected_prompt = example
        
        # Utiliser l'exemple sélectionné
        if hasattr(st.session_state, 'selected_prompt'):
            prompt = st.session_state.selected_prompt
            del st.session_state.selected_prompt
        
        # Génération
        if st.button("🎨 Générer l'image") and prompt:
            with st.spinner("🎨 Génération en cours... (30-90 secondes)"):
                result = generate_image_via_api(prompt, model_name, hf_token)
                
                if result["success"]:
                    try:
                        image = Image.open(io.BytesIO(result["image_bytes"]))
                        st.success("✅ Image générée avec succès !")
                        st.image(image, caption=f"Prompt: {prompt}", use_column_width=True)
                        
                        # Téléchargement
                        st.download_button(
                            label="💾 Télécharger",
                            data=result["image_bytes"],
                            file_name=f"generated_{hash(prompt) % 10000}.png",
                            mime="image/png"
                        )
                    except Exception as e:
                        st.error(f"❌ Erreur d'affichage : {str(e)}")
                else:
                    st.error(f"❌ {result['error']}")
                    
                    if "chargement" in result['error']:
                        st.info("💡 Réessayez dans quelques secondes")
                    elif "Token" in result['error']:
                        st.warning("🔑 Vérifiez votre token")
                    elif "non disponible" in result['error']:
                        st.info("💡 Essayez un autre modèle")

with tab2:
    st.header("🧪 Test des Modèles")
    
    if task_type == "Analyse de sentiment":
        text = st.text_input("Texte à analyser", "I love this application!")
        model = "cardiffnlp/twitter-roberta-base-sentiment-latest"
        
        if st.button("Analyser"):
            with st.spinner("Analyse..."):
                result = test_text_model_inference(model, text, hf_token)
                if result["success"]:
                    st.json(result["result"])
                else:
                    st.error(result["error"])
    
    elif task_type == "Génération de texte":
        prompt = st.text_input("Prompt", "Once upon a time")
        model = "gpt2"

        if st.button("Générer"):
            with st.spinner("Génération..."):
                result = test_text_model_inference(model, prompt, hf_token)
                if result["success"]:
                    st.json(result["result"])
                else:
                    st.error(result["error"])

    elif task_type == "Description d'images (Alternative)":
        st.info("🎨 Alternative à la génération d'images : génération de descriptions détaillées")

        prompt = st.text_area(
            "Décrivez le type d'image que vous voulez :",
            "Describe a beautiful landscape with mountains and a lake",
            height=100
        )

        model = st.selectbox(
            "Modèle de génération de texte",
            ["gpt2", "microsoft/DialoGPT-medium", "EleutherAI/gpt-neo-125M"]
        )

        if st.button("Générer une description"):
            with st.spinner("Génération de description..."):
                full_prompt = f"Create a detailed visual description: {prompt}. Description:"
                result = test_text_model_inference(model, full_prompt, hf_token)

                if result["success"]:
                    st.success("✅ Description générée :")

                    # Extraire le texte généré
                    if isinstance(result["result"], list) and len(result["result"]) > 0:
                        generated_text = result["result"][0].get("generated_text", "")
                        # Nettoyer le texte
                        description = generated_text.replace(full_prompt, "").strip()
                        st.write(description)

                        st.info("💡 **Conseil**: Utilisez cette description dans un générateur d'images comme DALL-E, Midjourney, ou Stable Diffusion local.")
                    else:
                        st.json(result["result"])
                else:
                    st.error(result["error"])

with tab3:
    st.header("📊 Informations")
    
    st.subheader("🎯 Fonctionnalités")
    st.write("✅ Interface pour génération d'images (avec limitations)")
    st.write("✅ Test de disponibilité des modèles")
    st.write("✅ Gestion d'erreurs améliorée")
    st.write("✅ Alternative : génération de descriptions d'images")
    st.write("✅ Analyse de sentiment fonctionnelle")
    st.write("✅ Génération de texte fonctionnelle")

    st.subheader("⚠️ Limitations importantes")
    st.write("❌ La plupart des modèles de génération d'images ne sont pas disponibles via l'API gratuite")
    st.write("❌ Stable Diffusion nécessite l'API payante ou installation locale")
    st.write("✅ Les modèles de texte fonctionnent parfaitement")

    st.subheader("🔧 Solutions recommandées")
    st.write("🔑 **API payante**: Hugging Face Inference Endpoints")
    st.write("💻 **Installation locale**: `pip install diffusers torch`")
    st.write("🌐 **Services externes**: OpenAI DALL-E, Midjourney")
    st.write("📝 **Alternative**: Génération de descriptions détaillées")

    if hf_token:
        st.subheader("🔑 Token")
        st.write(f"Token configuré: {hf_token[:10]}...")
        st.write("✅ Accès aux modèles de texte optimisé")

    st.subheader("💡 Conseils d'utilisation")
    st.write("• Les modèles de texte (GPT-2, sentiment) fonctionnent bien")
    st.write("• Pour les images, utilisez la fonction 'Description d'images'")
    st.write("• Installez diffusers localement pour la vraie génération d'images")
    st.write("• L'API payante donne accès à tous les modèles")

st.markdown("---")
st.markdown("🤗 Version corrigée avec génération d'images fonctionnelle")
