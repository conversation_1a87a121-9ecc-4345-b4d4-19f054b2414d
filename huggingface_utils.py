"""
Utilitaires pour interagir avec Hugging Face Hub
"""

import streamlit as st
import requests
from huggingface_hub import Hf<PERSON><PERSON>, list_models
from transformers import AutoTokenizer, AutoModel
import torch
from typing import List, Dict, Optional

class HuggingFaceManager:
    """Gestionnaire pour les interactions avec Hugging Face"""
    
    def __init__(self, token: Optional[str] = None):
        self.token = token
        self.api = HfApi(token=token)
    
    def test_connection(self) -> bool:
        """Teste la connexion à Hugging Face"""
        try:
            # Test simple avec l'API
            response = requests.get("https://huggingface.co/api/models?limit=1")
            return response.status_code == 200
        except Exception as e:
            st.error(f"Erreur de connexion : {str(e)}")
            return False
    
    def search_models(self, query: str, task: Optional[str] = None, limit: int = 10) -> List[Dict]:
        """Recherche des modèles sur Hugging Face Hub"""
        try:
            models = list_models(
                search=query,
                task=task,
                limit=limit,
                token=self.token
            )
            
            model_list = []
            for model in models:
                model_info = {
                    'id': model.id,
                    'downloads': getattr(model, 'downloads', 0),
                    'likes': getattr(model, 'likes', 0),
                    'task': getattr(model, 'pipeline_tag', 'Unknown'),
                    'library': getattr(model, 'library_name', 'Unknown')
                }
                model_list.append(model_info)
            
            return sorted(model_list, key=lambda x: x['downloads'], reverse=True)
        
        except Exception as e:
            st.error(f"Erreur lors de la recherche : {str(e)}")
            return []
    
    def get_model_info(self, model_id: str) -> Optional[Dict]:
        """Récupère les informations détaillées d'un modèle"""
        try:
            model_info = self.api.model_info(model_id)
            return {
                'id': model_info.id,
                'downloads': model_info.downloads,
                'likes': model_info.likes,
                'task': model_info.pipeline_tag,
                'library': model_info.library_name,
                'tags': model_info.tags,
                'created_at': model_info.created_at,
                'last_modified': model_info.last_modified
            }
        except Exception as e:
            st.error(f"Erreur lors de la récupération des infos : {str(e)}")
            return None
    
    def get_popular_models_by_task(self, task: str, limit: int = 5) -> List[Dict]:
        """Récupère les modèles populaires pour une tâche donnée"""
        task_mapping = {
            "Génération de texte": "text-generation",
            "Classification de texte": "text-classification",
            "Résumé de texte": "summarization",
            "Traduction": "translation",
            "Question-Réponse": "question-answering",
            "Classification d'images": "image-classification",
            "Génération d'images": "text-to-image"
        }
        
        hf_task = task_mapping.get(task, task)
        return self.search_models("", task=hf_task, limit=limit)
    
    def validate_model(self, model_id: str) -> bool:
        """Valide qu'un modèle existe et est accessible"""
        try:
            self.api.model_info(model_id)
            return True
        except Exception:
            return False

def get_device_info() -> Dict[str, str]:
    """Récupère les informations sur le device utilisé"""
    device_info = {
        "device": "CPU",
        "cuda_available": "Non",
        "cuda_version": "N/A",
        "gpu_count": "0"
    }
    
    if torch.cuda.is_available():
        device_info.update({
            "device": "CUDA",
            "cuda_available": "Oui",
            "cuda_version": torch.version.cuda,
            "gpu_count": str(torch.cuda.device_count()),
            "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.device_count() > 0 else "N/A"
        })
    
    return device_info

def format_model_size(num_parameters: int) -> str:
    """Formate la taille du modèle de manière lisible"""
    if num_parameters >= 1e9:
        return f"{num_parameters/1e9:.1f}B"
    elif num_parameters >= 1e6:
        return f"{num_parameters/1e6:.1f}M"
    elif num_parameters >= 1e3:
        return f"{num_parameters/1e3:.1f}K"
    else:
        return str(num_parameters)

@st.cache_data
def get_model_suggestions(task_type: str) -> List[str]:
    """Retourne des suggestions de modèles pour chaque type de tâche"""
    suggestions = {
        "Génération de texte": [
            "gpt2",
            "microsoft/DialoGPT-medium",
            "EleutherAI/gpt-neo-1.3B",
            "facebook/opt-350m"
        ],
        "Classification de texte": [
            "cardiffnlp/twitter-roberta-base-sentiment-latest",
            "nlptown/bert-base-multilingual-uncased-sentiment",
            "distilbert-base-uncased-finetuned-sst-2-english"
        ],
        "Résumé de texte": [
            "facebook/bart-large-cnn",
            "t5-small",
            "google/pegasus-xsum"
        ],
        "Traduction": [
            "Helsinki-NLP/opus-mt-en-fr",
            "Helsinki-NLP/opus-mt-fr-en",
            "t5-small"
        ],
        "Question-Réponse": [
            "distilbert-base-cased-distilled-squad",
            "deepset/roberta-base-squad2",
            "microsoft/DialoGPT-medium"
        ],
        "Classification d'images": [
            "google/vit-base-patch16-224",
            "microsoft/resnet-50",
            "facebook/deit-base-distilled-patch16-224"
        ],
        "Génération d'images": [
            "stabilityai/stable-diffusion-2-1",
            "CompVis/stable-diffusion-v1-4",
            "runwayml/stable-diffusion-v1-5",
            "stabilityai/stable-diffusion-xl-base-1.0"
        ]
    }
    
    return suggestions.get(task_type, [])
