"""
Test de la génération d'images avec l'API Hugging Face
"""

import requests
import os
from dotenv import load_dotenv
from PIL import Image
import io

# Charger les variables d'environnement
load_dotenv()

def test_image_generation():
    """Test complet de la génération d'images"""
    
    print("🎨 Test de Génération d'Images Hugging Face")
    print("=" * 50)
    
    # Récupérer le token
    token = os.getenv("HUGGINGFACE_API_TOKEN")
    if token:
        print(f"✅ Token trouvé: {token[:10]}...")
    else:
        print("⚠️ Aucun token trouvé - fonctionnalités limitées")
    
    # Modèles à tester
    models_to_test = [
        "stabilityai/stable-diffusion-2-1",
        "CompVis/stable-diffusion-v1-4",
        "runwayml/stable-diffusion-v1-5"
    ]
    
    prompt = "A beautiful sunset over mountains, digital art style"
    
    for i, model in enumerate(models_to_test, 1):
        print(f"\n{i}. Test du modèle: {model}")
        print("-" * 40)
        
        # Test de disponibilité
        try:
            url = f"https://api-inference.huggingface.co/models/{model}"
            headers = {"Content-Type": "application/json"}
            
            if token:
                headers["Authorization"] = f"Bearer {token}"
            
            # Test rapide
            test_payload = {"inputs": "test"}
            response = requests.post(url, headers=headers, json=test_payload, timeout=10)
            
            if response.status_code == 200:
                print("✅ Modèle disponible et prêt")
                status = "ready"
            elif response.status_code == 503:
                print("⏳ Modèle en cours de chargement")
                status = "loading"
            elif response.status_code == 404:
                print("❌ Modèle non trouvé")
                status = "not_found"
                continue
            elif response.status_code == 401:
                print("🔑 Problème d'authentification")
                status = "auth_error"
                continue
            else:
                print(f"⚠️ Status inattendu: {response.status_code}")
                status = "unknown"
                continue
            
        except Exception as e:
            print(f"❌ Erreur de connexion: {e}")
            continue
        
        # Test de génération si le modèle est disponible
        if status in ["ready", "loading"]:
            print(f"🎨 Test de génération avec le prompt: '{prompt}'")
            
            try:
                payload = {"inputs": prompt}
                response = requests.post(url, headers=headers, json=payload, timeout=60)
                
                if response.status_code == 200:
                    if len(response.content) > 1000:
                        print("✅ Image générée avec succès !")
                        print(f"   Taille: {len(response.content)} bytes")
                        
                        # Sauvegarder l'image pour test
                        try:
                            image = Image.open(io.BytesIO(response.content))
                            filename = f"test_image_{model.replace('/', '_')}.png"
                            image.save(filename)
                            print(f"   💾 Image sauvegardée: {filename}")
                            print(f"   📐 Dimensions: {image.size}")
                        except Exception as e:
                            print(f"   ⚠️ Erreur de sauvegarde: {e}")
                    else:
                        print("❌ Réponse trop petite (pas une image valide)")
                
                elif response.status_code == 503:
                    print("⏳ Modèle encore en chargement")
                else:
                    print(f"❌ Erreur génération: {response.status_code}")
                    print(f"   Détails: {response.text[:100]}...")
            
            except requests.exceptions.Timeout:
                print("⏱️ Timeout - La génération a pris trop de temps")
            except Exception as e:
                print(f"❌ Erreur génération: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Résumé du test:")
    print("- Testez les modèles dans l'application Streamlit")
    print("- Les modèles peuvent prendre du temps à se charger")
    print("- Utilisez des prompts en anglais pour de meilleurs résultats")
    print("- La génération peut prendre 30-90 secondes")
    
    print("\n💡 Conseils pour l'application Streamlit:")
    print("1. Sélectionnez 'Génération d'images' dans le type de tâche")
    print("2. Choisissez un modèle dans la liste déroulante")
    print("3. Testez d'abord la disponibilité du modèle")
    print("4. Utilisez des prompts détaillés en anglais")
    print("5. Soyez patient - la génération peut prendre du temps")

if __name__ == "__main__":
    test_image_generation()
