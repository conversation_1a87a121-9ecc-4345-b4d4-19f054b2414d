"""
Application Flask simple pour Hugging Face
Alternative à Streamlit si celui-ci pose problème
"""

from flask import Flask, render_template_string, request, jsonify
import requests
import os
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

app = Flask(__name__)

# Template HTML simple
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>🤗 Hugging Face Explorer</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }
        button { background-color: #ff6b35; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        button:hover { background-color: #e55a2b; }
        .result { margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border-left: 4px solid #ff6b35; }
        .error { background-color: #f8d7da; border-left-color: #dc3545; }
        .success { background-color: #d4edda; border-left-color: #28a745; }
        .token-status { padding: 10px; margin-bottom: 20px; border-radius: 5px; }
        .token-ok { background-color: #d4edda; color: #155724; }
        .token-missing { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤗 Hugging Face Explorer</h1>
            <p>Testez les modèles Hugging Face facilement</p>
        </div>
        
        {% if token %}
        <div class="token-status token-ok">
            ✅ Token configuré: {{ token[:10] }}...
        </div>
        {% else %}
        <div class="token-status token-missing">
            ⚠️ Aucun token configuré (fonctionnalités limitées)
        </div>
        {% endif %}
        
        <form method="POST" action="/test">
            <div class="form-group">
                <label for="model">Modèle Hugging Face:</label>
                <select name="model" id="model">
                    <option value="cardiffnlp/twitter-roberta-base-sentiment-latest">Analyse de sentiment</option>
                    <option value="gpt2">Génération de texte</option>
                    <option value="distilbert-base-cased-distilled-squad">Question-Réponse</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="text">Texte d'entrée:</label>
                <textarea name="text" id="text" rows="4" placeholder="Entrez votre texte ici...">I love this application!</textarea>
            </div>
            
            <button type="submit">🧪 Tester le modèle</button>
        </form>
        
        {% if result %}
        <div class="result {{ 'success' if result.success else 'error' }}">
            <h3>{{ '✅ Résultat:' if result.success else '❌ Erreur:' }}</h3>
            <pre>{{ result.data }}</pre>
        </div>
        {% endif %}
        
        <div style="margin-top: 40px; text-align: center; color: #666;">
            <p>🤗 Powered by Hugging Face | Créé avec Flask</p>
        </div>
    </div>
</body>
</html>
"""

def test_model_inference(model_id, text, token=None):
    """Teste l'inférence d'un modèle"""
    try:
        url = f"https://api-inference.huggingface.co/models/{model_id}"
        headers = {"Content-Type": "application/json"}
        
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        payload = {"inputs": text}
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        if response.status_code == 200:
            return {"success": True, "data": response.json()}
        else:
            return {"success": False, "data": f"Status {response.status_code}: {response.text}"}
    
    except Exception as e:
        return {"success": False, "data": str(e)}

@app.route('/')
def index():
    token = os.getenv("HUGGINGFACE_API_TOKEN")
    return render_template_string(HTML_TEMPLATE, token=token, result=None)

@app.route('/test', methods=['POST'])
def test():
    model = request.form.get('model')
    text = request.form.get('text')
    token = os.getenv("HUGGINGFACE_API_TOKEN")
    
    if not text:
        result = {"success": False, "data": "Veuillez entrer du texte"}
    else:
        result = test_model_inference(model, text, token)
    
    return render_template_string(HTML_TEMPLATE, token=token, result=result)

if __name__ == '__main__':
    print("🤗 Démarrage de l'application Hugging Face...")
    print("📍 Accédez à: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
