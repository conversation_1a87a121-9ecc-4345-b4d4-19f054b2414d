"""
Application Streamlit basique pour Hugging Face
Version simplifiée qui fonctionne avec les modules de base
"""

import streamlit as st
import requests
import json
import os
from typing import Dict, List, Optional
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

# Configuration de la page
st.set_page_config(
    page_title="🤗 Hugging Face Basic",
    page_icon="🤗",
    layout="wide"
)

def test_huggingface_connection() -> bool:
    """Teste la connexion à Hugging Face Hub"""
    try:
        response = requests.get("https://huggingface.co/api/models?limit=1", timeout=10)
        return response.status_code == 200
    except Exception:
        return False

def search_models(query: str = "", limit: int = 5) -> List[Dict]:
    """Recherche des modèles via l'API REST de Hugging Face"""
    try:
        url = f"https://huggingface.co/api/models"
        params = {
            "limit": limit,
            "search": query
        }
        
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            models = response.json()
            return [
                {
                    "id": model.get("id", "Unknown"),
                    "downloads": model.get("downloads", 0),
                    "likes": model.get("likes", 0),
                    "task": model.get("pipeline_tag", "Unknown")
                }
                for model in models
            ]
        else:
            return []
            
    except Exception:
        return []

def test_model_inference(model_id: str, text: str, token: Optional[str] = None) -> Dict:
    """Teste l'inférence d'un modèle via l'API Inference"""
    try:
        url = f"https://api-inference.huggingface.co/models/{model_id}"
        
        headers = {
            "Content-Type": "application/json"
        }
        
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        payload = {"inputs": text}
        
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        if response.status_code == 200:
            return {"success": True, "result": response.json()}
        else:
            return {"success": False, "error": f"Status: {response.status_code}"}
            
    except Exception as e:
        return {"success": False, "error": str(e)}

# Interface principale
st.title("🤗 Hugging Face Explorer - Version Basique")
st.markdown("Explorez et testez les modèles Hugging Face facilement !")

# Sidebar
st.sidebar.header("⚙️ Configuration")

# Token Hugging Face
default_token = os.getenv("HUGGINGFACE_API_TOKEN", "")
hf_token = st.sidebar.text_input(
    "Token Hugging Face",
    type="password",
    value=default_token,
    help="Chargé automatiquement depuis .env ou entrez manuellement"
)

# Afficher le statut du token
if hf_token:
    st.sidebar.success(f"✅ Token configuré ({hf_token[:10]}...)")
else:
    st.sidebar.warning("⚠️ Aucun token configuré")

# Test de connexion
st.sidebar.subheader("🌐 Connexion")
if st.sidebar.button("🔄 Tester la connexion"):
    with st.spinner("Test en cours..."):
        is_connected = test_huggingface_connection()
        if is_connected:
            st.sidebar.success("✅ Connexion réussie !")
        else:
            st.sidebar.error("❌ Problème de connexion")

# Interface principale
tab1, tab2, tab3 = st.tabs(["🔍 Recherche", "🧪 Test d'Inférence", "📊 Informations"])

with tab1:
    st.header("Recherche de Modèles")
    
    col1, col2 = st.columns([3, 1])
    
    with col1:
        search_query = st.text_input("🔍 Rechercher des modèles", placeholder="Ex: sentiment, translation, gpt...")
    
    with col2:
        search_limit = st.selectbox("Nombre de résultats", [5, 10, 20], index=0)
    
    if st.button("🔍 Rechercher") and search_query:
        with st.spinner("Recherche en cours..."):
            models = search_models(search_query, limit=search_limit)
            
            if models:
                st.success(f"✅ {len(models)} modèles trouvés")
                
                for model in models:
                    with st.expander(f"📦 {model['id']} - {model['task']}"):
                        col1, col2, col3 = st.columns(3)
                        
                        with col1:
                            st.metric("📥 Téléchargements", f"{model['downloads']:,}")
                        
                        with col2:
                            st.metric("❤️ Likes", model['likes'])
                        
                        with col3:
                            st.write(f"**Tâche**: {model['task']}")
                        
                        # Boutons d'action
                        col1, col2 = st.columns(2)
                        with col1:
                            st.code(model['id'])
                        
                        with col2:
                            st.markdown(f"[🔗 Voir sur HF Hub](https://huggingface.co/{model['id']})")
            else:
                st.warning("Aucun modèle trouvé pour cette recherche")

with tab2:
    st.header("🧪 Test d'Inférence")
    
    # Modèles prédéfinis pour les tests
    predefined_models = {
        "Sentiment Analysis": "cardiffnlp/twitter-roberta-base-sentiment-latest",
        "Text Generation": "gpt2",
        "Question Answering": "distilbert-base-cased-distilled-squad"
    }
    
    model_choice = st.selectbox("Choisir un modèle", list(predefined_models.keys()))
    model_id = predefined_models[model_choice]
    
    st.write(f"**Modèle sélectionné**: `{model_id}`")
    
    # Interface selon le type de modèle
    if model_choice == "Sentiment Analysis":
        text_input = st.text_area("Texte à analyser", "I love this application!")
        
        if st.button("🔍 Analyser le sentiment"):
            with st.spinner("Analyse en cours..."):
                result = test_model_inference(model_id, text_input, hf_token)
                
                if result["success"]:
                    st.success("✅ Analyse réussie !")
                    st.json(result["result"])
                else:
                    st.error(f"❌ Erreur: {result['error']}")
    
    elif model_choice == "Text Generation":
        prompt = st.text_area("Prompt de génération", "Once upon a time")
        
        if st.button("✨ Générer du texte"):
            with st.spinner("Génération en cours..."):
                result = test_model_inference(model_id, prompt, hf_token)
                
                if result["success"]:
                    st.success("✅ Génération réussie !")
                    st.json(result["result"])
                else:
                    st.error(f"❌ Erreur: {result['error']}")
    
    elif model_choice == "Question Answering":
        context = st.text_area("Contexte", "Paris is the capital of France.")
        question = st.text_input("Question", "What is the capital of France?")
        
        if st.button("❓ Répondre"):
            if context and question:
                with st.spinner("Recherche de la réponse..."):
                    # Format spécial pour QA
                    qa_input = {
                        "question": question,
                        "context": context
                    }
                    
                    try:
                        url = f"https://api-inference.huggingface.co/models/{model_id}"
                        headers = {"Content-Type": "application/json"}
                        if hf_token:
                            headers["Authorization"] = f"Bearer {hf_token}"
                        
                        response = requests.post(url, headers=headers, json=qa_input, timeout=30)
                        
                        if response.status_code == 200:
                            result = response.json()
                            st.success("✅ Réponse trouvée !")
                            st.json(result)
                        else:
                            st.error(f"❌ Erreur: Status {response.status_code}")
                    
                    except Exception as e:
                        st.error(f"❌ Erreur: {str(e)}")
            else:
                st.warning("Veuillez remplir le contexte et la question")

with tab3:
    st.header("📊 Informations")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("🎯 Fonctionnalités")
        st.write("✅ Recherche de modèles")
        st.write("✅ Test d'inférence basique")
        st.write("✅ Support API Inference")
        st.write("✅ Interface simple")
        
        st.subheader("🔧 Prochaines étapes")
        st.write("📦 Installer transformers pour plus de fonctionnalités")
        st.write("🖼️ Support pour les images")
        st.write("🎵 Support pour l'audio")
    
    with col2:
        st.subheader("💡 Conseils")
        st.write("🔑 Utilisez un token HF pour de meilleures performances")
        st.write("⚡ L'API Inference peut être lente au premier appel")
        st.write("🎯 Testez différents modèles selon vos besoins")
        st.write("📚 Consultez la documentation HF pour plus d'infos")
        
        st.subheader("🌐 Liens utiles")
        st.markdown("[🤗 Hugging Face Hub](https://huggingface.co)")
        st.markdown("[📖 Documentation](https://huggingface.co/docs)")
        st.markdown("[🔑 Obtenir un token](https://huggingface.co/settings/tokens)")

# Footer
st.markdown("---")
st.markdown("🤗 Version basique | Créé avec Streamlit")
