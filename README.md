# Application Hugging Face avec Streamlit

Cette application vous permet d'interagir facilement avec les modèles Hugging Face via une interface web intuitive.

## Fonctionnalités

- 🤗 Connexion à Hugging Face Hub
- 📝 Génération de texte
- 🔍 Classification de texte
- 🖼️ Analyse d'images
- 🎯 Support pour différents types de modèles

## Installation

1. Clonez ce repository
2. Installez les dépendances :
```bash
pip install -r requirements.txt
```

3. (Optionnel) Configurez votre token Hugging Face :
   - Copiez `.env.example` vers `.env`
   - Ajoutez votre token Hugging Face

## Utilisation

Lancez l'application :
```bash
streamlit run app.py
```

L'application sera accessible sur `http://localhost:8501`

## Configuration

Pour utiliser des modèles privés ou avoir des limites plus élevées, obtenez un token sur :
https://huggingface.co/settings/tokens
